<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SlsData extends Model
{
    protected $table = 'sls_data';

    protected $fillable = [
        'kdkec',
        'nmkec',
        'kddesa',
        'nmdesa',
        'id_sls',
        'nama_sls',
    ];

    /**
     * Get SLS data by district and village
     */
    public static function getSlsByVillage($kdkec, $kddesa)
    {
        return self::where('kdkec', $kdkec)
                   ->where('kddesa', $kddesa)
                   ->orderBy('id_sls')
                   ->get();
    }

    /**
     * Get formatted SLS data for dropdown
     */
    public static function getSlsForDropdown($kdkec, $kddesa)
    {
        $slsData = self::getSlsByVillage($kdkec, $kddesa);
        $formatted = [];
        
        foreach ($slsData as $sls) {
            $formatted[$sls->id_sls] = $sls->id_sls . ' - ' . $sls->nama_sls;
        }
        
        return $formatted;
    }

    /**
     * Get SLS name by ID
     */
    public static function getSlsName($id_sls)
    {
        $sls = self::where('id_sls', $id_sls)->first();
        return $sls ? $sls->nama_sls : null;
    }

    /**
     * Get all unique districts from SLS data
     */
    public static function getDistrictsFromSls()
    {
        return self::select('kdkec', 'nmkec')
                   ->distinct()
                   ->orderBy('kdkec')
                   ->get()
                   ->pluck('nmkec', 'kdkec')
                   ->toArray();
    }

    /**
     * Get villages by district from SLS data
     */
    public static function getVillagesByDistrictFromSls($kdkec)
    {
        return self::select('kddesa', 'nmdesa')
                   ->where('kdkec', $kdkec)
                   ->distinct()
                   ->orderBy('kddesa')
                   ->get()
                   ->pluck('nmdesa', 'kddesa')
                   ->toArray();
    }
}
