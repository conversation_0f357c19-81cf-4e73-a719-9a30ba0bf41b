<?php $__env->startSection('title', 'T<PERSON><PERSON> Wilkerstat - HaloIP'); ?>

<?php $__env->startSection('content'); ?>
<div class="haloip-container">
    <div class="haloip-content">
        <!-- Header Section -->
        <div class="haloip-header">
            <div class="haloip-header-content">
                <div class="haloip-header-text">
                    <h1 class="haloip-title">Tiket Wilkerstat</h1>
                    <p class="haloip-subtitle">Sistem tiket untuk survei statistik dan masalah SLS (Statistical Location Segment)</p>
                </div>
                
                <!-- Navigation Tabs -->
                <div class="haloip-nav">
                    <a href="<?php echo e(route('tickets.index')); ?>" class="haloip-nav-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                        </svg>
                        Tiket IT
                    </a>
                    <a href="<?php echo e(route('map-requests.index')); ?>" class="haloip-nav-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polygon points="3 6 9 1 15 6 21 1 21 14 15 9 9 14 3 9"></polygon>
                            <line x1="9" y1="1" x2="9" y2="14"></line>
                            <line x1="15" y1="6" x2="15" y2="9"></line>
                        </svg>
                        Permintaan Peta
                    </a>
                    <a href="<?php echo e(route('wilkerstat.index')); ?>" class="haloip-nav-item active">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 3v18h18"></path>
                            <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
                        </svg>
                        Tiket Wilkerstat
                    </a>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        <?php if(request('request_success')): ?>
            <div class="haloip-alert haloip-alert-success">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                </svg>
                Tiket Wilkerstat berhasil diajukan! Tim IT akan segera menindaklanjuti permintaan Anda.
            </div>
        <?php endif; ?>

        <!-- Filter Section -->
        <div class="haloip-filter-section">
            <form method="GET" action="<?php echo e(route('wilkerstat.index')); ?>" class="haloip-filter-form">
                <div class="haloip-filter-grid">
                    <div>
                        <label class="haloip-form-label">Bulan</label>
                        <select name="month" class="haloip-form-control">
                            <option value="">Semua Bulan</option>
                            <?php for($i = 1; $i <= 12; $i++): ?>
                                <option value="<?php echo e($i); ?>" <?php echo e(request('month') == $i ? 'selected' : ''); ?>>
                                    <?php echo e(DateTime::createFromFormat('!m', $i)->format('F')); ?>

                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div>
                        <label class="haloip-form-label">IT Staff</label>
                        <select name="it_staff" class="haloip-form-control">
                            <option value="">Semua IT Staff</option>
                            <?php $__currentLoopData = $itStaffList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $staff): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($staff->id); ?>" <?php echo e(request('it_staff') == $staff->id ? 'selected' : ''); ?>><?php echo e($staff->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div>
                        <label class="haloip-form-label">Kecamatan</label>
                        <select name="kdkec" class="haloip-form-control">
                            <option value="">Semua Kecamatan</option>
                            <?php $__currentLoopData = \App\Services\LocationService::getDistrictsForDropdown(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($code); ?>" <?php echo e(request('kdkec') == $code ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div>
                        <label class="haloip-form-label">Status</label>
                        <div class="relative">
                            <button type="button" class="haloip-form-control flex items-center justify-between" id="statusDropdown">
                                <span>Pilih Status</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m6 9 6 6 6-6"/>
                                </svg>
                            </button>
                            <div class="haloip-dropdown-menu" id="statusDropdownMenu">
                                <label class="haloip-checkbox-item">
                                    <input type="checkbox" name="status[]" value="pending" <?php echo e(in_array('pending', request('status', [])) ? 'checked' : ''); ?>>
                                    <span>Menunggu</span>
                                </label>
                                <label class="haloip-checkbox-item">
                                    <input type="checkbox" name="status[]" value="in_progress" <?php echo e(in_array('in_progress', request('status', [])) ? 'checked' : ''); ?>>
                                    <span>Sedang Diproses</span>
                                </label>
                                <label class="haloip-checkbox-item">
                                    <input type="checkbox" name="status[]" value="completed" <?php echo e(in_array('completed', request('status', [])) ? 'checked' : ''); ?>>
                                    <span>Selesai</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="haloip-filter-actions">
                    <button type="submit" class="haloip-btn haloip-btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                        Filter
                    </button>
                    <a href="<?php echo e(route('wilkerstat.index')); ?>" class="haloip-btn haloip-btn-secondary">Reset</a>
                </div>
            </form>
        </div>

        <!-- Action Buttons -->
        <div class="haloip-actions">
            <a href="<?php echo e(route('wilkerstat.create')); ?>" class="haloip-btn haloip-btn-success">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Ajukan Tiket Wilkerstat
            </a>
            <?php if(Auth::user()->is_it_staff): ?>
                <a href="<?php echo e(route('wilkerstat.manage')); ?>" class="haloip-btn haloip-btn-danger">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                    </svg>
                    Kelola Tiket
                </a>
            <?php endif; ?>
        </div>

        <!-- Wilkerstat Tickets Table -->
        <div class="haloip-table-container haloip-table-loading w-full overflow-x-auto relative">
            <?php if($wilkerstatTickets->count() > 0): ?>
                <table class="haloip-table w-full table-fixed">
                    <thead>
                        <tr>
                            <th class="w-1/4 min-w-0">Tiket & Detail</th>
                            <th class="w-1/5 min-w-0">Pengaju & Kategori</th>
                            <th class="w-1/5 min-w-0">Lokasi SLS</th>
                            <th class="w-1/6 min-w-0">Status & IT Staff</th>
                            <th class="w-1/6 min-w-0">Tanggal</th>
                            <th class="w-1/4 min-w-0">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $wilkerstatTickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-code"><?php echo e($ticket->ticket_code); ?></div>
                                    <div class="haloip-table-title"><?php echo e($ticket->title); ?></div>
                                    <div class="haloip-table-meta"><?php echo e(Str::limit($ticket->description, 80)); ?></div>
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-title"><?php echo e($ticket->requestor->name ?? 'Pengaju Tidak Diketahui'); ?></div>
                                    <div class="haloip-table-meta"><?php echo e($ticket->problem_category); ?></div>
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-meta">
                                        <?php if($ticket->nmkec): ?>
                                            <strong>Kec:</strong> <?php echo e($ticket->nmkec); ?><br>
                                        <?php endif; ?>
                                        <?php if($ticket->nmdesa): ?>
                                            <strong>Kel:</strong> <?php echo e($ticket->nmdesa); ?><br>
                                        <?php endif; ?>
                                        <?php if($ticket->nama_sls): ?>
                                            <strong>SLS:</strong> <?php echo e($ticket->nama_sls); ?>

                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-status status-<?php echo e($ticket->status); ?>">
                                        <?php echo e($ticket->status == 'pending' ? 'Menunggu' : ($ticket->status == 'in_progress' ? 'Sedang Diproses' : 'Selesai')); ?>

                                    </div>
                                    <div class="haloip-table-meta mt-2">
                                        <strong>IT Staff:</strong> <?php echo e($ticket->itStaff->name ?? 'Belum Ditugaskan'); ?>

                                    </div>
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-meta">
                                        <strong>Diajukan:</strong><br>
                                        <?php echo e($ticket->created_at->locale('id')->isoFormat('D MMMM Y')); ?>

                                    </div>
                                    <?php if($ticket->done_at): ?>
                                        <div class="haloip-table-meta mt-2">
                                            <strong>Selesai:</strong><br>
                                            <?php echo e($ticket->done_at->locale('id')->isoFormat('D MMMM Y')); ?>

                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-actions">
                                        <a href="<?php echo e(route('public.view', $ticket->public_token)); ?>" class="haloip-btn haloip-btn-sm haloip-btn-info" target="_blank">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                <circle cx="12" cy="12" r="3"></circle>
                                            </svg>
                                            Lihat
                                        </a>
                                        <?php if(Auth::user()->is_it_staff): ?>
                                            <a href="<?php echo e(route('wilkerstat.show', $ticket)); ?>" class="haloip-btn haloip-btn-sm haloip-btn-primary">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                                    <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                                </svg>
                                                Kelola
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="haloip-empty-state">
                    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 3v18h18"></path>
                        <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
                    </svg>
                    <h3>Belum Ada Tiket Wilkerstat</h3>
                    <p>Belum ada tiket Wilkerstat yang diajukan. Klik tombol "Ajukan Tiket Wilkerstat" untuk membuat tiket baru.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if($wilkerstatTickets->hasPages()): ?>
            <div class="haloip-pagination">
                <?php echo e($wilkerstatTickets->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status dropdown functionality
    const statusDropdown = document.getElementById('statusDropdown');
    const statusDropdownMenu = document.getElementById('statusDropdownMenu');

    if (statusDropdown && statusDropdownMenu) {
        statusDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            statusDropdownMenu.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!statusDropdown.contains(e.target) && !statusDropdownMenu.contains(e.target)) {
                statusDropdownMenu.classList.remove('show');
            }
        });

        // Update dropdown text based on selected checkboxes
        const checkboxes = statusDropdownMenu.querySelectorAll('input[type="checkbox"]');
        const updateDropdownText = () => {
            const selected = Array.from(checkboxes).filter(cb => cb.checked);
            const span = statusDropdown.querySelector('span');
            if (selected.length === 0) {
                span.textContent = 'Pilih Status';
            } else if (selected.length === 1) {
                span.textContent = selected[0].nextElementSibling.textContent;
            } else {
                span.textContent = `${selected.length} Status Dipilih`;
            }
        };

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateDropdownText);
        });

        // Initialize dropdown text
        updateDropdownText();
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('haloip.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\trackprogresstim\resources\views/haloip/wilkerstat/index.blade.php ENDPATH**/ ?>