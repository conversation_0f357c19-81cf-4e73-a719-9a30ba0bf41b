<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sls_data', function (Blueprint $table) {
            $table->id();
            $table->string('kdkec', 3); // District code (e.g., '010')
            $table->string('nmkec'); // District name (e.g., 'BELAKANG PADANG')
            $table->string('kddesa', 3); // Village code (e.g., '001')
            $table->string('nmdesa'); // Village name (e.g., 'PULAU TERONG')
            $table->string('id_sls', 14); // SLS ID (e.g., '21710100010001')
            $table->string('nama_sls'); // SLS name (e.g., 'RT 001 RW 001')
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['kdkec', 'kddesa']);
            $table->index('kdkec');
            $table->unique(['kdkec', 'kddesa', 'id_sls']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sls_data');
    }
};
