@extends('haloip.layouts.app')

@section('title', 'Detail Tiket Wilkerstat - HaloIP')

@section('content')
<div class="haloip-container">
    <div class="haloip-content">
        <!-- Header Section -->
        <div class="haloip-header">
            <div class="haloip-header-content">
                <div class="haloip-header-text">
                    <h1 class="haloip-title">Detail Tiket Wilkerstat</h1>
                    <p class="haloip-subtitle">{{ $ticket->ticket_code }}</p>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="haloip-back-button">
            <a href="{{ route('wilkerstat.manage') }}" class="haloip-btn haloip-btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m12 19-7-7 7-7"></path>
                    <path d="M19 12H5"></path>
                </svg>
                Kembali ke Kelola Tiket
            </a>
        </div>

        <!-- Ticket Details -->
        <div class="haloip-detail-container">
            <div class="haloip-detail-grid">
                <!-- Left Column - Ticket Information -->
                <div class="haloip-detail-main">
                    <div class="haloip-detail-card">
                        <h3 class="haloip-detail-title">Informasi Tiket</h3>
                        
                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Kode Tiket</label>
                            <div class="haloip-detail-value">{{ $ticket->ticket_code }}</div>
                        </div>

                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Judul</label>
                            <div class="haloip-detail-value">{{ $ticket->title }}</div>
                        </div>

                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Kategori Masalah</label>
                            <div class="haloip-detail-value">{{ $ticket->problem_category }}</div>
                        </div>

                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Deskripsi</label>
                            <div class="haloip-detail-value">{{ $ticket->description }}</div>
                        </div>

                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Status</label>
                            <div class="haloip-detail-value">
                                <span class="haloip-status-badge status-{{ $ticket->status }}">
                                    {{ $ticket->status == 'pending' ? 'Menunggu' : ($ticket->status == 'in_progress' ? 'Sedang Diproses' : 'Selesai') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Location Information -->
                    <div class="haloip-detail-card">
                        <h3 class="haloip-detail-title">Informasi Lokasi SLS</h3>
                        
                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Kecamatan</label>
                            <div class="haloip-detail-value">{{ $ticket->kdkec }} - {{ $ticket->nmkec }}</div>
                        </div>

                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Kelurahan</label>
                            <div class="haloip-detail-value">{{ $ticket->kddesa }} - {{ $ticket->nmdesa }}</div>
                        </div>

                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">ID SLS</label>
                            <div class="haloip-detail-value">{{ $ticket->id_sls }}</div>
                        </div>

                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Nama SLS</label>
                            <div class="haloip-detail-value">{{ $ticket->nama_sls }}</div>
                        </div>
                    </div>

                    <!-- Photos Section -->
                    @if($ticket->requestor_photo || $ticket->it_photo)
                        <div class="haloip-detail-card">
                            <h3 class="haloip-detail-title">Foto</h3>
                            
                            <div class="haloip-photo-grid">
                                @if($ticket->requestor_photo)
                                    <div class="haloip-photo-item">
                                        <label class="haloip-detail-label">Foto dari Pengaju</label>
                                        <img src="{{ asset('storage/' . $ticket->requestor_photo) }}" alt="Foto Pengaju" class="haloip-photo">
                                    </div>
                                @endif

                                @if($ticket->it_photo)
                                    <div class="haloip-photo-item">
                                        <label class="haloip-detail-label">Foto dari IT Staff</label>
                                        <img src="{{ asset('storage/' . $ticket->it_photo) }}" alt="Foto IT Staff" class="haloip-photo">
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Right Column - Management Panel -->
                <div class="haloip-detail-sidebar">
                    <!-- Assignment Card -->
                    <div class="haloip-detail-card">
                        <h3 class="haloip-detail-title">Penugasan</h3>
                        
                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Pengaju</label>
                            <div class="haloip-detail-value">{{ $ticket->requestor->name ?? 'Pengaju Tidak Diketahui' }}</div>
                        </div>

                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">IT Staff</label>
                            <div class="haloip-detail-value">{{ $ticket->itStaff->name ?? 'Belum Ditugaskan' }}</div>
                        </div>

                        <div class="haloip-detail-item">
                            <label class="haloip-detail-label">Tanggal Dibuat</label>
                            <div class="haloip-detail-value">{{ $ticket->created_at->locale('id')->isoFormat('D MMMM Y, HH:mm') }}</div>
                        </div>

                        @if($ticket->done_at)
                            <div class="haloip-detail-item">
                                <label class="haloip-detail-label">Tanggal Selesai</label>
                                <div class="haloip-detail-value">{{ $ticket->done_at->locale('id')->isoFormat('D MMMM Y, HH:mm') }}</div>
                            </div>
                        @endif
                    </div>

                    <!-- Management Form -->
                    <div class="haloip-detail-card">
                        <h3 class="haloip-detail-title">Kelola Tiket</h3>
                        
                        <form action="{{ route('wilkerstat.update', $ticket) }}" method="POST" enctype="multipart/form-data" class="haloip-form">
                            @csrf
                            @method('PUT')

                            <div class="haloip-form-group">
                                <label for="it_staff_id" class="haloip-form-label">IT Staff</label>
                                <select id="it_staff_id" name="it_staff_id" class="haloip-form-control">
                                    <option value="">Pilih IT Staff</option>
                                    @foreach($itStaffList as $staff)
                                        <option value="{{ $staff->id }}" {{ $ticket->it_staff_id == $staff->id ? 'selected' : '' }}>
                                            {{ $staff->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="haloip-form-group">
                                <label for="status" class="haloip-form-label">Status</label>
                                <select id="status" name="status" class="haloip-form-control">
                                    <option value="pending" {{ $ticket->status == 'pending' ? 'selected' : '' }}>Menunggu</option>
                                    <option value="in_progress" {{ $ticket->status == 'in_progress' ? 'selected' : '' }}>Sedang Diproses</option>
                                    <option value="completed" {{ $ticket->status == 'completed' ? 'selected' : '' }}>Selesai</option>
                                </select>
                            </div>

                            <div class="haloip-form-group">
                                <label for="it_notes" class="haloip-form-label">Catatan IT</label>
                                <textarea id="it_notes" name="it_notes" rows="3" class="haloip-form-control" placeholder="Tambahkan catatan...">{{ $ticket->it_notes }}</textarea>
                            </div>

                            <div class="haloip-form-group">
                                <label for="it_photo" class="haloip-form-label">Upload Foto IT</label>
                                <input type="file" id="it_photo" name="it_photo" class="haloip-form-control" accept="image/*">
                                <div class="haloip-form-help">Format yang didukung: JPG, PNG, GIF. Maksimal 2MB.</div>
                            </div>

                            <div class="haloip-form-actions">
                                <button type="submit" class="haloip-btn haloip-btn-primary haloip-btn-block">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                        <polyline points="17,21 17,13 7,13 7,21"></polyline>
                                        <polyline points="7,3 7,8 15,8"></polyline>
                                    </svg>
                                    Simpan Perubahan
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Quick Actions -->
                    <div class="haloip-detail-card">
                        <h3 class="haloip-detail-title">Aksi Cepat</h3>
                        
                        <div class="haloip-quick-actions">
                            <a href="{{ route('public.view', $ticket->public_token) }}" class="haloip-btn haloip-btn-info haloip-btn-block" target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                </svg>
                                Lihat Publik
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
