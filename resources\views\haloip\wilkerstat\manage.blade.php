@extends('haloip.layouts.app')

@section('title', 'Kelola Tiket Wilkerstat - HaloIP')

@section('content')
<div class="haloip-container">
    <div class="haloip-content">
        <!-- Header Section -->
        <div class="haloip-header">
            <div class="haloip-header-content">
                <div class="haloip-header-text">
                    <h1 class="haloip-title">Kelola Tiket Wilkerstat</h1>
                    <p class="haloip-subtitle">Kelola dan pantau semua tiket Wilkerstat yang masuk</p>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="haloip-back-button">
            <a href="{{ route('wilkerstat.index') }}" class="haloip-btn haloip-btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m12 19-7-7 7-7"></path>
                    <path d="M19 12H5"></path>
                </svg>
                Kembali ke HaloIP
            </a>
        </div>

        <!-- Filter Section -->
        <div class="haloip-filter-section">
            <form method="GET" action="{{ route('wilkerstat.manage') }}" class="haloip-filter-form">
                <div class="haloip-filter-grid">
                    <div>
                        <label class="haloip-form-label">Bulan</label>
                        <select name="month" class="haloip-form-control">
                            <option value="">Semua Bulan</option>
                            @for ($i = 1; $i <= 12; $i++)
                                <option value="{{ $i }}" {{ request('month') == $i ? 'selected' : '' }}>
                                    {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                </option>
                            @endfor
                        </select>
                    </div>
                    <div>
                        <label class="haloip-form-label">IT Staff</label>
                        <select name="it_staff" class="haloip-form-control">
                            <option value="">Semua IT Staff</option>
                            @foreach ($itStaffList as $staff)
                                <option value="{{ $staff->id }}" {{ request('it_staff') == $staff->id ? 'selected' : '' }}>{{ $staff->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label class="haloip-form-label">Kecamatan</label>
                        <select name="kdkec" class="haloip-form-control">
                            <option value="">Semua Kecamatan</option>
                            @foreach (\App\Services\LocationService::getDistrictsForDropdown() as $code => $label)
                                <option value="{{ $code }}" {{ request('kdkec') == $code ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label class="haloip-form-label">Status</label>
                        <div class="relative">
                            <button type="button" class="haloip-form-control flex items-center justify-between" id="statusDropdown">
                                <span>Pilih Status</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m6 9 6 6 6-6"/>
                                </svg>
                            </button>
                            <div class="haloip-dropdown-menu" id="statusDropdownMenu">
                                <label class="haloip-checkbox-item">
                                    <input type="checkbox" name="status[]" value="pending" {{ in_array('pending', request('status', [])) ? 'checked' : '' }}>
                                    <span>Menunggu</span>
                                </label>
                                <label class="haloip-checkbox-item">
                                    <input type="checkbox" name="status[]" value="in_progress" {{ in_array('in_progress', request('status', [])) ? 'checked' : '' }}>
                                    <span>Sedang Diproses</span>
                                </label>
                                <label class="haloip-checkbox-item">
                                    <input type="checkbox" name="status[]" value="completed" {{ in_array('completed', request('status', [])) ? 'checked' : '' }}>
                                    <span>Selesai</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="haloip-filter-actions">
                    <button type="submit" class="haloip-btn haloip-btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                        Filter
                    </button>
                    <a href="{{ route('wilkerstat.manage') }}" class="haloip-btn haloip-btn-secondary">Reset</a>
                </div>
            </form>
        </div>

        <!-- Statistics Cards -->
        <div class="haloip-stats-grid">
            <div class="haloip-stat-card">
                <div class="haloip-stat-icon bg-yellow-100 text-yellow-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12,6 12,12 16,14"></polyline>
                    </svg>
                </div>
                <div class="haloip-stat-content">
                    <div class="haloip-stat-number">{{ $stats['pending'] }}</div>
                    <div class="haloip-stat-label">Menunggu</div>
                </div>
            </div>

            <div class="haloip-stat-card">
                <div class="haloip-stat-icon bg-blue-100 text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                    </svg>
                </div>
                <div class="haloip-stat-content">
                    <div class="haloip-stat-number">{{ $stats['in_progress'] }}</div>
                    <div class="haloip-stat-label">Sedang Diproses</div>
                </div>
            </div>

            <div class="haloip-stat-card">
                <div class="haloip-stat-icon bg-green-100 text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22,4 12,14.01 9,11.01"></polyline>
                    </svg>
                </div>
                <div class="haloip-stat-content">
                    <div class="haloip-stat-number">{{ $stats['completed'] }}</div>
                    <div class="haloip-stat-label">Selesai</div>
                </div>
            </div>

            <div class="haloip-stat-card">
                <div class="haloip-stat-icon bg-gray-100 text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 3v18h18"></path>
                        <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
                    </svg>
                </div>
                <div class="haloip-stat-content">
                    <div class="haloip-stat-number">{{ $stats['total'] }}</div>
                    <div class="haloip-stat-label">Total Tiket</div>
                </div>
            </div>
        </div>

        <!-- Wilkerstat Tickets Table -->
        <div class="haloip-table-container haloip-table-loading w-full overflow-x-auto relative">
            @if ($wilkerstatTickets->count() > 0)
                <table class="haloip-table w-full table-fixed">
                    <thead>
                        <tr>
                            <th class="w-1/4 min-w-0">Tiket & Detail</th>
                            <th class="w-1/5 min-w-0">Pengaju & Kategori</th>
                            <th class="w-1/5 min-w-0">Lokasi SLS</th>
                            <th class="w-1/6 min-w-0">Status & IT Staff</th>
                            <th class="w-1/6 min-w-0">Tanggal</th>
                            <th class="w-1/4 min-w-0">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($wilkerstatTickets as $ticket)
                            <tr>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-code">{{ $ticket->ticket_code }}</div>
                                    <div class="haloip-table-title">{{ $ticket->title }}</div>
                                    <div class="haloip-table-meta">{{ Str::limit($ticket->description, 80) }}</div>
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-title">{{ $ticket->requestor->name ?? 'Pengaju Tidak Diketahui' }}</div>
                                    <div class="haloip-table-meta">{{ $ticket->problem_category }}</div>
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-meta">
                                        @if($ticket->nmkec)
                                            <strong>Kec:</strong> {{ $ticket->nmkec }}<br>
                                        @endif
                                        @if($ticket->nmdesa)
                                            <strong>Kel:</strong> {{ $ticket->nmdesa }}<br>
                                        @endif
                                        @if($ticket->nama_sls)
                                            <strong>SLS:</strong> {{ $ticket->nama_sls }}
                                        @endif
                                    </div>
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-status status-{{ $ticket->status }}">
                                        {{ $ticket->status == 'pending' ? 'Menunggu' : ($ticket->status == 'in_progress' ? 'Sedang Diproses' : 'Selesai') }}
                                    </div>
                                    <div class="haloip-table-meta mt-2">
                                        <strong>IT Staff:</strong> {{ $ticket->itStaff->name ?? 'Belum Ditugaskan' }}
                                    </div>
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-meta">
                                        <strong>Diajukan:</strong><br>
                                        {{ $ticket->created_at->locale('id')->isoFormat('D MMMM Y') }}
                                    </div>
                                    @if ($ticket->done_at)
                                        <div class="haloip-table-meta mt-2">
                                            <strong>Selesai:</strong><br>
                                            {{ $ticket->done_at->locale('id')->isoFormat('D MMMM Y') }}
                                        </div>
                                    @endif
                                </td>
                                <td class="p-3 sm:p-4">
                                    <div class="haloip-table-actions">
                                        <a href="{{ route('wilkerstat.show', $ticket) }}" class="haloip-btn haloip-btn-sm haloip-btn-primary">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                            </svg>
                                            Kelola
                                        </a>
                                        <a href="{{ route('public.view', $ticket->public_token) }}" class="haloip-btn haloip-btn-sm haloip-btn-info" target="_blank">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                <circle cx="12" cy="12" r="3"></circle>
                                            </svg>
                                            Lihat
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div class="haloip-empty-state">
                    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 3v18h18"></path>
                        <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
                    </svg>
                    <h3>Belum Ada Tiket Wilkerstat</h3>
                    <p>Belum ada tiket Wilkerstat yang perlu dikelola.</p>
                </div>
            @endif
        </div>

        <!-- Pagination -->
        @if ($wilkerstatTickets->hasPages())
            <div class="haloip-pagination">
                {{ $wilkerstatTickets->links() }}
            </div>
        @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status dropdown functionality
    const statusDropdown = document.getElementById('statusDropdown');
    const statusDropdownMenu = document.getElementById('statusDropdownMenu');

    if (statusDropdown && statusDropdownMenu) {
        statusDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            statusDropdownMenu.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!statusDropdown.contains(e.target) && !statusDropdownMenu.contains(e.target)) {
                statusDropdownMenu.classList.remove('show');
            }
        });

        // Update dropdown text based on selected checkboxes
        const checkboxes = statusDropdownMenu.querySelectorAll('input[type="checkbox"]');
        const updateDropdownText = () => {
            const selected = Array.from(checkboxes).filter(cb => cb.checked);
            const span = statusDropdown.querySelector('span');
            if (selected.length === 0) {
                span.textContent = 'Pilih Status';
            } else if (selected.length === 1) {
                span.textContent = selected[0].nextElementSibling.textContent;
            } else {
                span.textContent = `${selected.length} Status Dipilih`;
            }
        };

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateDropdownText);
        });

        // Initialize dropdown text
        updateDropdownText();
    }
});
</script>
@endsection
