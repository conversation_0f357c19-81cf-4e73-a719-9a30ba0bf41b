<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            // Add Wilkerstat-specific fields
            $table->string('problem_category')->nullable()->after('nmdesa'); // Problem category for Wilkerstat tickets
            $table->string('id_sls', 14)->nullable()->after('problem_category'); // SLS ID
            $table->string('nama_sls')->nullable()->after('id_sls'); // SLS name
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropColumn(['problem_category', 'id_sls', 'nama_sls']);
        });
    }
};
