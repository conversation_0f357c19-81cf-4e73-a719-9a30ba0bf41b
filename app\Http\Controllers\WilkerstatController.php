<?php

namespace App\Http\Controllers;

use App\Models\Ticket;
use App\Models\SlsData;
use App\Models\User;
use App\Services\LocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Arr;

class WilkerstatController extends Controller
{
    public function index(Request $request)
    {
        $query = Ticket::with(['requestor', 'itStaff'])->where('service_type', 'wilkerstat');

        if ($month = $request->input('month')) {
            $query->whereMonth('created_at', $month);
        }

        if ($itStaff = $request->input('it_staff')) {
            $query->where('it_staff_id', $itStaff);
        }

        $statuses = Arr::wrap($request->input('status', []));
        if (!empty($statuses)) {
            $query->whereIn('status', $statuses);
        }

        if ($kdkec = $request->input('kdkec')) {
            $query->where('kdkec', $kdkec);
        }

        if ($problemCategory = $request->input('problem_category')) {
            $query->where('problem_category', $problemCategory);
        }

        $wilkerstatTickets = $query->latest()->paginate(10);
        $itStaffList = User::where('is_it_staff', true)->get();

        return view('haloip.wilkerstat.index', compact('wilkerstatTickets', 'itStaffList'));
    }

    public function create()
    {
        $problemCategories = [
            'Kawasan perizinan lokasi khusus',
            'Identifikasi SLS yang mekar',
            'Perubahan batas wilayah',
            'Kesalahan data koordinat',
            'Duplikasi data SLS',
            'SLS tidak ditemukan di lapangan',
            'Perubahan nama SLS',
            'Lainnya'
        ];

        $districts = LocationService::getDistrictsForDropdown();

        return view('haloip.wilkerstat.create', compact('problemCategories', 'districts'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required',
            'requestor_photo' => 'nullable|image|max:2048',
            'problem_category' => 'required|string',
            'kdkec' => 'required|string',
            'kddesa' => 'required|string',
            'id_sls' => 'required|string',
        ]);

        $photoPath = null;
        if ($request->hasFile('requestor_photo')) {
            $photoPath = $request->file('requestor_photo')->store('wilkerstat/requestors', 'public');
        }

        // Get location names
        $nmkec = LocationService::getDistrictName($request->kdkec);
        $nmdesa = LocationService::getVillageName($request->kdkec, $request->kddesa);
        $nama_sls = SlsData::getSlsName($request->id_sls);

        Ticket::create([
            'ticket_code' => Ticket::generateTicketCode('wilkerstat'),
            'user_id' => Auth::id(),
            'ruangan' => 'Wilkerstat Survey', // Default value for Wilkerstat tickets
            'title' => $request->title,
            'description' => $request->description,
            'requestor_photo' => $photoPath,
            'service_type' => 'wilkerstat',
            'problem_category' => $request->problem_category,
            'kdkec' => $request->kdkec,
            'nmkec' => $nmkec,
            'kddesa' => $request->kddesa,
            'nmdesa' => $nmdesa,
            'id_sls' => $request->id_sls,
            'nama_sls' => $nama_sls,
            'public_token' => Ticket::generatePublicToken(),
        ]);

        return redirect('/haloIP/wilkerstat?request_success=true')->with('success', 'Tiket Wilkerstat berhasil diajukan!');
    }

    public function manage(Request $request)
    {
        $query = Ticket::where('it_staff_id', auth()->id())->where('service_type', 'wilkerstat');

        if ($month = $request->input('month')) {
            $query->whereMonth('created_at', $month);
        }

        if ($statuses = $request->input('status')) {
            $query->whereIn('status', $statuses);
        }

        $wilkerstatTickets = $query->latest()->paginate(10);

        return view('wilkerstat.manage', compact('wilkerstatTickets'));
    }

    public function show(Ticket $wilkerstatTicket)
    {
        if ($wilkerstatTicket->service_type !== 'wilkerstat') {
            abort(404, 'Wilkerstat ticket not found.');
        }

        if ($wilkerstatTicket->it_staff_id !== Auth::id() && $wilkerstatTicket->it_staff_id !== null) {
            abort(403, 'Unauthorized access to this Wilkerstat ticket.');
        }

        return view('wilkerstat.show', compact('wilkerstatTicket'));
    }

    public function update(Request $request, Ticket $wilkerstatTicket)
    {
        if ($wilkerstatTicket->service_type !== 'wilkerstat') {
            abort(404, 'Wilkerstat ticket not found.');
        }

        $request->validate([
            'status' => 'required|in:pending,in_progress,completed',
            'it_photo' => 'nullable|image|max:2048',
        ]);

        $updateData = [
            'status' => $request->status,
            'it_staff_id' => Auth::id(),
        ];

        if ($request->status === 'completed') {
            $updateData['done_at'] = now();
        }

        if ($request->hasFile('it_photo')) {
            $updateData['it_photo'] = $request->file('it_photo')->store('wilkerstat/it_staff', 'public');
        }

        $wilkerstatTicket->update($updateData);

        return redirect()->back()->with('success', 'Status tiket berhasil diperbarui!');
    }

    // API endpoint for getting villages by district
    public function getVillagesByDistrict(Request $request, $districtCode)
    {
        $villages = LocationService::getVillagesForDropdown($districtCode);
        return response()->json($villages);
    }

    // API endpoint for getting SLS by village
    public function getSlsByVillage(Request $request, $districtCode, $villageCode)
    {
        $slsData = SlsData::getSlsForDropdown($districtCode, $villageCode);
        return response()->json($slsData);
    }

    // API endpoint for getting SLS name by ID
    public function getSlsName(Request $request, $slsId)
    {
        $slsName = SlsData::getSlsName($slsId);
        return response()->json(['nama_sls' => $slsName]);
    }

    // Get Wilkerstat tickets for API
    public function getWilkerstatTickets(Request $request)
    {
        $query = Ticket::with(['requestor', 'itStaff'])->where('service_type', 'wilkerstat');

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('it_staff_id')) {
            $query->where('it_staff_id', $request->it_staff_id);
        }

        $wilkerstatTickets = $query->latest()->get();

        return response()->json($wilkerstatTickets);
    }
}
