@extends('haloip.layouts.app')

@section('title', 'Ajukan Tiket Wilkerstat - HaloIP')

@section('content')
<div class="haloip-container">
    <div class="haloip-content">
        <!-- Header Section -->
        <div class="haloip-header">
            <div class="haloip-header-content">
                <div class="haloip-header-text">
                    <h1 class="haloip-title">Ajukan Tiket Wilkerstat</h1>
                    <p class="haloip-subtitle">Laporkan masalah terkait survei statistik dan SLS (Statistical Location Segment)</p>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="haloip-back-button">
            <a href="{{ route('wilkerstat.index') }}" class="haloip-btn haloip-btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m12 19-7-7 7-7"></path>
                    <path d="M19 12H5"></path>
                </svg>
                Kembali ke Tiket Wilkerstat
            </a>
        </div>

        <!-- Form Section -->
        <div class="haloip-form-container">
            <form action="{{ route('wilkerstat.store') }}" method="POST" enctype="multipart/form-data" class="haloip-form">
                @csrf

                <!-- Basic Information -->
                <div class="haloip-form-section">
                    <h3 class="haloip-form-section-title">Informasi Dasar</h3>
                    
                    <div class="haloip-form-grid">
                        <div class="haloip-form-group">
                            <label for="title" class="haloip-form-label">Judul Tiket <span class="text-red-500">*</span></label>
                            <input type="text" id="title" name="title" class="haloip-form-control" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="haloip-form-error">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="haloip-form-group">
                            <label for="problem_category" class="haloip-form-label">Kategori Masalah <span class="text-red-500">*</span></label>
                            <select id="problem_category" name="problem_category" class="haloip-form-control" required>
                                <option value="">Pilih Kategori Masalah</option>
                                @foreach($problemCategories as $category)
                                    <option value="{{ $category }}" {{ old('problem_category') == $category ? 'selected' : '' }}>{{ $category }}</option>
                                @endforeach
                            </select>
                            @error('problem_category')
                                <div class="haloip-form-error">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="haloip-form-group">
                        <label for="description" class="haloip-form-label">Deskripsi Masalah <span class="text-red-500">*</span></label>
                        <textarea id="description" name="description" rows="4" class="haloip-form-control" placeholder="Jelaskan masalah yang Anda hadapi secara detail..." required>{{ old('description') }}</textarea>
                        @error('description')
                            <div class="haloip-form-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Location Information -->
                <div class="haloip-form-section">
                    <h3 class="haloip-form-section-title">Informasi Lokasi SLS</h3>
                    
                    <div class="haloip-form-grid">
                        <div class="haloip-form-group">
                            <label for="kdkec" class="haloip-form-label">Kecamatan <span class="text-red-500">*</span></label>
                            <select id="kdkec" name="kdkec" class="haloip-form-control" required>
                                <option value="">Pilih Kecamatan</option>
                                @foreach($districts as $code => $name)
                                    <option value="{{ $code }}" {{ old('kdkec') == $code ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                            @error('kdkec')
                                <div class="haloip-form-error">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="haloip-form-group">
                            <label for="kddesa" class="haloip-form-label">Kelurahan <span class="text-red-500">*</span></label>
                            <select id="kddesa" name="kddesa" class="haloip-form-control" required disabled>
                                <option value="">Pilih Kelurahan</option>
                            </select>
                            @error('kddesa')
                                <div class="haloip-form-error">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="haloip-form-grid">
                        <div class="haloip-form-group">
                            <label for="id_sls" class="haloip-form-label">ID SLS <span class="text-red-500">*</span></label>
                            <select id="id_sls" name="id_sls" class="haloip-form-control" required disabled>
                                <option value="">Pilih ID SLS</option>
                            </select>
                            @error('id_sls')
                                <div class="haloip-form-error">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="haloip-form-group">
                            <label for="nama_sls_display" class="haloip-form-label">Nama SLS</label>
                            <input type="text" id="nama_sls_display" class="haloip-form-control" readonly placeholder="Akan terisi otomatis setelah memilih ID SLS">
                        </div>
                    </div>
                </div>

                <!-- Photo Upload -->
                <div class="haloip-form-section">
                    <h3 class="haloip-form-section-title">Foto Pendukung</h3>
                    
                    <div class="haloip-form-group">
                        <label for="requestor_photo" class="haloip-form-label">Upload Foto (Opsional)</label>
                        <input type="file" id="requestor_photo" name="requestor_photo" class="haloip-form-control" accept="image/*">
                        <div class="haloip-form-help">Format yang didukung: JPG, PNG, GIF. Maksimal 2MB.</div>
                        @error('requestor_photo')
                            <div class="haloip-form-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="haloip-form-actions">
                    <button type="submit" class="haloip-btn haloip-btn-primary haloip-btn-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22,4 12,14.01 9,11.01"></polyline>
                        </svg>
                        Ajukan Tiket Wilkerstat
                    </button>
                    <a href="{{ route('wilkerstat.index') }}" class="haloip-btn haloip-btn-secondary haloip-btn-lg">Batal</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const kdkecSelect = document.getElementById('kdkec');
    const kddesaSelect = document.getElementById('kddesa');
    const idSlsSelect = document.getElementById('id_sls');
    const namaSlsDisplay = document.getElementById('nama_sls_display');

    // Function to load villages based on selected district
    function loadVillages(districtCode) {
        if (!districtCode) {
            kddesaSelect.innerHTML = '<option value="">Pilih Kelurahan</option>';
            kddesaSelect.disabled = true;
            resetSls();
            return;
        }

        kddesaSelect.disabled = true;
        kddesaSelect.innerHTML = '<option value="">Loading...</option>';

        fetch(`/api/wilkerstat/villages/${districtCode}`)
            .then(response => response.json())
            .then(data => {
                kddesaSelect.innerHTML = '<option value="">Pilih Kelurahan</option>';
                Object.entries(data).forEach(([code, name]) => {
                    const option = document.createElement('option');
                    option.value = code;
                    option.textContent = name;
                    kddesaSelect.appendChild(option);
                });
                kddesaSelect.disabled = false;
                
                // Restore old value if exists
                const oldValue = '{{ old("kddesa") }}';
                if (oldValue) {
                    kddesaSelect.value = oldValue;
                    loadSls(districtCode, oldValue);
                }
            })
            .catch(error => {
                console.error('Error loading villages:', error);
                kddesaSelect.innerHTML = '<option value="">Error loading villages</option>';
                kddesaSelect.disabled = false;
            });
    }

    // Function to load SLS based on selected district and village
    function loadSls(districtCode, villageCode) {
        if (!districtCode || !villageCode) {
            resetSls();
            return;
        }

        idSlsSelect.disabled = true;
        idSlsSelect.innerHTML = '<option value="">Loading...</option>';

        fetch(`/api/wilkerstat/sls/${districtCode}/${villageCode}`)
            .then(response => response.json())
            .then(data => {
                idSlsSelect.innerHTML = '<option value="">Pilih ID SLS</option>';
                Object.entries(data).forEach(([id, name]) => {
                    const option = document.createElement('option');
                    option.value = id;
                    option.textContent = name;
                    idSlsSelect.appendChild(option);
                });
                idSlsSelect.disabled = false;
                
                // Restore old value if exists
                const oldValue = '{{ old("id_sls") }}';
                if (oldValue) {
                    idSlsSelect.value = oldValue;
                    loadSlsName(oldValue);
                }
            })
            .catch(error => {
                console.error('Error loading SLS:', error);
                idSlsSelect.innerHTML = '<option value="">Error loading SLS</option>';
                idSlsSelect.disabled = false;
            });
    }

    // Function to load SLS name based on selected SLS ID
    function loadSlsName(slsId) {
        if (!slsId) {
            namaSlsDisplay.value = '';
            return;
        }

        fetch(`/api/wilkerstat/sls-name/${slsId}`)
            .then(response => response.json())
            .then(data => {
                namaSlsDisplay.value = data.nama_sls || '';
            })
            .catch(error => {
                console.error('Error loading SLS name:', error);
                namaSlsDisplay.value = '';
            });
    }

    // Function to reset SLS fields
    function resetSls() {
        idSlsSelect.innerHTML = '<option value="">Pilih ID SLS</option>';
        idSlsSelect.disabled = true;
        namaSlsDisplay.value = '';
    }

    // Event listeners
    kdkecSelect.addEventListener('change', function() {
        const districtCode = this.value;
        loadVillages(districtCode);
        resetSls();
    });

    kddesaSelect.addEventListener('change', function() {
        const villageCode = this.value;
        const districtCode = kdkecSelect.value;
        loadSls(districtCode, villageCode);
    });

    idSlsSelect.addEventListener('change', function() {
        const slsId = this.value;
        loadSlsName(slsId);
    });

    // Initialize on page load if old values exist
    const oldKdkec = '{{ old("kdkec") }}';
    if (oldKdkec) {
        loadVillages(oldKdkec);
    }
});
</script>
@endsection
