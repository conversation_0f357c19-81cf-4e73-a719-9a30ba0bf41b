<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SlsDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $slsData = [
            // BELAKANG PADANG - PULAU TERONG
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '001', 'nmdesa' => 'PULAU TERONG', 'id_sls' => '21710100010001', 'nama_sls' => 'RT 001 RW 001'],
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '001', 'nmdesa' => 'PULAU TERONG', 'id_sls' => '21710100010002', 'nama_sls' => 'RT 002 RW 001'],
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '001', 'nmdesa' => 'PULAU TERONG', 'id_sls' => '21710100010003', 'nama_sls' => 'RT 003 RW 001'],
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '001', 'nmdesa' => 'PULAU TERONG', 'id_sls' => '21710100010004', 'nama_sls' => 'RT 004 RW 001'],
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '001', 'nmdesa' => 'PULAU TERONG', 'id_sls' => '21710100010005', 'nama_sls' => 'RT 005 RW 001'],

            // BELAKANG PADANG - PECONG
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '002', 'nmdesa' => 'PECONG', 'id_sls' => '21710100020001', 'nama_sls' => 'RT 001 RW 001'],
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '002', 'nmdesa' => 'PECONG', 'id_sls' => '21710100020002', 'nama_sls' => 'RT 002 RW 001'],
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '002', 'nmdesa' => 'PECONG', 'id_sls' => '21710100020003', 'nama_sls' => 'RT 003 RW 001'],
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '002', 'nmdesa' => 'PECONG', 'id_sls' => '21710100020004', 'nama_sls' => 'RT 004 RW 001'],

            // BELAKANG PADANG - KASU
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '003', 'nmdesa' => 'KASU', 'id_sls' => '21710100030001', 'nama_sls' => 'RT 001 RW 001'],
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '003', 'nmdesa' => 'KASU', 'id_sls' => '21710100030002', 'nama_sls' => 'RT 002 RW 001'],
            ['kdkec' => '010', 'nmkec' => 'BELAKANG PADANG', 'kddesa' => '003', 'nmdesa' => 'KASU', 'id_sls' => '21710100030003', 'nama_sls' => 'RT 003 RW 001'],

            // Add more sample data for other districts
            // BULANG - PANTAI GELAM
            ['kdkec' => '020', 'nmkec' => 'BULANG', 'kddesa' => '001', 'nmdesa' => 'PANTAI GELAM', 'id_sls' => '21710200010001', 'nama_sls' => 'RT 001 RW 001'],
            ['kdkec' => '020', 'nmkec' => 'BULANG', 'kddesa' => '001', 'nmdesa' => 'PANTAI GELAM', 'id_sls' => '21710200010002', 'nama_sls' => 'RT 002 RW 001'],
            ['kdkec' => '020', 'nmkec' => 'BULANG', 'kddesa' => '001', 'nmdesa' => 'PANTAI GELAM', 'id_sls' => '21710200010003', 'nama_sls' => 'RT 003 RW 001'],

            // GALANG - PULAU ABANG
            ['kdkec' => '030', 'nmkec' => 'GALANG', 'kddesa' => '001', 'nmdesa' => 'PULAU ABANG', 'id_sls' => '21710300010001', 'nama_sls' => 'RT 001 RW 001'],
            ['kdkec' => '030', 'nmkec' => 'GALANG', 'kddesa' => '001', 'nmdesa' => 'PULAU ABANG', 'id_sls' => '21710300010002', 'nama_sls' => 'RT 002 RW 001'],
        ];

        DB::table('sls_data')->insert($slsData);
    }
}
